<?php
/**
 * اختبار بسيط لدوال لوحة المعلومات
 * Simple test for dashboard functions
 */

// محاكاة البيئة المطلوبة
class MockConfig {
    private $data = [
        'config_currency' => 'EGP',
        'config_language_id' => 1,
        'config_monthly_sales_target' => 100000
    ];
    
    public function get($key) {
        return $this->data[$key] ?? null;
    }
}

class MockDB {
    public function escape($value) {
        return addslashes($value);
    }
    
    public function query($sql) {
        // محاكاة نتائج الاستعلامات
        $mock_results = [
            'row' => [
                'total' => 150,
                'low_stock' => 25,
                'out_of_stock' => 5,
                'total_value' => 250000.50,
                'total_quantity' => 1500
            ],
            'rows' => [],
            'num_rows' => 1
        ];
        
        return (object)$mock_results;
    }
}

class MockModel {
    public $config;
    public $db;
    
    public function __construct() {
        $this->config = new MockConfig();
        $this->db = new MockDB();
    }
}

// تضمين ملف dashboard.php (محاكاة)
require_once 'dashboard/model/common/dashboard.php';

// إنشاء instance للاختبار
class TestDashboard extends ModelCommonDashboard {
    public function __construct() {
        $this->config = new MockConfig();
        $this->db = new MockDB();
    }
}

// تشغيل الاختبارات
echo "=== اختبار دوال لوحة المعلومات ===\n\n";

try {
    $dashboard = new TestDashboard();
    
    // اختبار دالة إحصائيات المخزون
    echo "1. اختبار إحصائيات المخزون:\n";
    $inventory_stats = $dashboard->getInventoryStats();
    echo "   - إجمالي المنتجات: " . $inventory_stats['total_products'] . "\n";
    echo "   - منخفض المخزون: " . $inventory_stats['low_stock_count'] . "\n";
    echo "   - نافد المخزون: " . $inventory_stats['out_of_stock_count'] . "\n";
    echo "   - قيمة المخزون: " . $inventory_stats['total_value'] . " " . $inventory_stats['currency'] . "\n";
    echo "   - الحالة: " . $inventory_stats['status_text'] . "\n\n";
    
    // اختبار دالة إحصائيات المبيعات
    echo "2. اختبار إحصائيات المبيعات:\n";
    $sales_stats = $dashboard->getSalesStats();
    echo "   - مبيعات اليوم: " . $sales_stats['today_sales'] . " " . $sales_stats['currency'] . "\n";
    echo "   - طلبات اليوم: " . $sales_stats['today_orders'] . "\n";
    echo "   - متوسط الطلب: " . $sales_stats['today_avg_order'] . " " . $sales_stats['currency'] . "\n";
    echo "   - نسبة تحقيق الهدف: " . $sales_stats['achievement_rate'] . "%\n";
    echo "   - الحالة: " . $sales_stats['status_text'] . "\n\n";
    
    // اختبار دالة التحقق من وجود الجداول
    echo "3. اختبار التحقق من وجود الجداول:\n";
    $tables_to_check = ['product', 'order', 'customer', 'user'];
    foreach ($tables_to_check as $table) {
        $exists = $dashboard->tableExists($table);
        echo "   - جدول cod_$table: " . ($exists ? "موجود ✅" : "غير موجود ❌") . "\n";
    }
    echo "\n";
    
    echo "✅ تم إكمال جميع الاختبارات بنجاح!\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في الاختبار: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء الاختبارات ===\n";
?>
